# Lightning Shuffler - Scratchpad

## MVP Plan (v0.1)
- Dark neon theme with glassmorphism
- Core entities: Playlist, Mix, Video
- Features: Add playlist by URL, list videos, local persistence, basic queue, play with YouTube embed, controls (play/pause/prev/next/reshuffle), search bar filters list
- Storage: Tauri v2 plugin-store or fs JSON in app data
- YouTube Data API v3: fetch playlist items, cache thumbnails/titles
- System tray: show current track, quick controls (next/prev/pause)
- Media keys integration: map to play/pause/next/prev

## Data Models
- Playlist: { id, title, url, videos[], lastRefreshed }
- Video: { id, title, channelTitle, thumbnail, durationSec }
- Mix: { id, title, sourcePlaylistIds[], videos[] (flattened) }
- AppState: { playlists, mixes, queue, currentIndex, loopCount, settings }

## API
- getPlaylistDetails(url): resolve playlistId, fetch items (paginated)
- refreshPlaylist(id)
- createMix(name, playlistIds)
- saveState/loadState

## UI Structure
- App shell: Sidebar (search+queue/history), Main player, Controls bar
- Modal: Add playlist, Create mix
- Components: Sidebar, SearchBar, VideoItem, Player, Controls, TrayStatus

## Notes
- Respect Tauri CSP, allow YouTube embed
- Secure API key but for now local env
- Animate reshuffle and loop counter

## Todo (Next Pass)
- Implement tray + system media keys in Rust with tauri-plugin-global-shortcut, tauri-plugin-shell?
- Keyboard shortcuts
- Help overlay