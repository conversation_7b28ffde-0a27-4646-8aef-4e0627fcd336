{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 17114967381396467289, "deps": [[376837177317575824, "build_script_build", false, 8472467913044641039], [4143744114649553716, "raw_window_handle", false, 4572721544728011781], [5986029879202738730, "log", false, 769167750787707049], [10281541584571964250, "windows_sys", false, 17722635671871324537]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-7a151f432a49c5a9\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}