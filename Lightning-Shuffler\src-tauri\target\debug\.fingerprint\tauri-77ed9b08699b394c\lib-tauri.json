{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 5247324318306713526, "deps": [[40386456601120721, "percent_encoding", false, 762266957637005826], [654232091421095663, "tauri_utils", false, 1219994895049865922], [1200537532907108615, "url<PERSON><PERSON>n", false, 10928541493328989523], [1967864351173319501, "muda", false, 7411357396225925645], [2013030631243296465, "webview2_com", false, 16292405638108799478], [3150220818285335163, "url", false, 2932393668862457855], [3331586631144870129, "getrandom", false, 17444841090713916895], [4143744114649553716, "raw_window_handle", false, 4572721544728011781], [4919829919303820331, "serialize_to_javascript", false, 7867498509092585380], [5986029879202738730, "log", false, 769167750787707049], [9010263965687315507, "http", false, 5530258970695073684], [9689903380558560274, "serde", false, 5874487516112888390], [10229185211513642314, "mime", false, 3855418565933258797], [10806645703491011684, "thiserror", false, 14013279725810500109], [11989259058781683633, "dunce", false, 6706533162972636214], [12092653563678505622, "build_script_build", false, 1809226593289636163], [12304025191202589669, "tauri_runtime_wry", false, 14672934779464787158], [12565293087094287914, "window_vibrancy", false, 9360893659250350419], [12943761728066819757, "tauri_runtime", false, 4822173334274463840], [12986574360607194341, "serde_repr", false, 468182309118276333], [13077543566650298139, "heck", false, 10264810602801001668], [13405681745520956630, "tauri_macros", false, 9657175886237139222], [13625485746686963219, "anyhow", false, 1399948433417357107], [14585479307175734061, "windows", false, 11065999775011887023], [16362055519698394275, "serde_json", false, 9609641283525214307], [16928111194414003569, "dirs", false, 13694690660984277414], [17155886227862585100, "glob", false, 3292550539767500518], [17531218394775549125, "tokio", false, 10775982368843430234], [18035788301859549979, "tray_icon", false, 12581579536240457539]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-77ed9b08699b394c\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}