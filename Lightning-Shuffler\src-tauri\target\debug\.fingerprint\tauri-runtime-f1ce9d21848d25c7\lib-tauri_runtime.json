{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 5940844529563334275, "deps": [[654232091421095663, "tauri_utils", false, 1219994895049865922], [3150220818285335163, "url", false, 2932393668862457855], [4143744114649553716, "raw_window_handle", false, 4572721544728011781], [7606335748176206944, "dpi", false, 16559739753826327783], [9010263965687315507, "http", false, 5530258970695073684], [9689903380558560274, "serde", false, 5874487516112888390], [10806645703491011684, "thiserror", false, 14013279725810500109], [12943761728066819757, "build_script_build", false, 6952000158800095780], [14585479307175734061, "windows", false, 11065999775011887023], [16362055519698394275, "serde_json", false, 9609641283525214307], [16727543399706004146, "cookie", false, 7118081021915188201]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-f1ce9d21848d25c7\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}