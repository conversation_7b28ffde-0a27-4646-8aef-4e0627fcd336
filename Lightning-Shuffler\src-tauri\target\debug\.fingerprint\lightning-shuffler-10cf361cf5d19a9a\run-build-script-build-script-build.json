{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[791294565694001944, "build_script_build", false, 1116005863923008506], [12092653563678505622, "build_script_build", false, 1809226593289636163], [16702348383442838006, "build_script_build", false, 12200393672547295334]], "local": [{"RerunIfChanged": {"output": "debug\\build\\lightning-shuffler-10cf361cf5d19a9a\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}