import "./App.css";
import { IconPlayerPlay, IconPlayerPause, IconPlayerSkipForward, IconPlayerSkipBack, IconArrowsShuffle, IconRepeat, IconInfinity, IconPlayerPlayFilled, IconPlayerPauseFilled } from "@tabler/icons-react";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { invoke } from "@tauri-apps/api/core";

// --- Types ---
type Video = { id: string; title: string; channelTitle: string; thumbnail: string };
type Playlist = { id: string; title: string; url: string; videos: Video[]; lastRefreshed?: string };

// --- Constants ---
const YT_API_KEY = "AIzaSyAFGB8-5IffhA-sAtvt7MYQJLwQJZTPypI";

// --- Helpers ---
function extractPlaylistId(url: string): string | null {
  try {
    const u = new URL(url);
    if (u.searchParams.get("list")) return u.searchParams.get("list");
    return null;
  } catch {
    return null;
  }
}

async function fetchPlaylistItems(playlistId: string): Promise<{ title: string; videos: Video[] }> {
  const base = "https://www.googleapis.com/youtube/v3/playlistItems";
  const part = "snippet";
  const maxResults = 50;
  let pageToken: string | undefined = undefined;
  const videos: Video[] = [];
  let playlistTitle = "";

  do {
    const url = new URL(base);
    url.searchParams.set("key", YT_API_KEY);
    url.searchParams.set("part", part);
    url.searchParams.set("playlistId", playlistId);
    url.searchParams.set("maxResults", String(maxResults));
    if (pageToken) url.searchParams.set("pageToken", pageToken);

    const res = await fetch(url.toString());
    if (!res.ok) throw new Error(`YouTube API error: ${res.status}`);
    const data = await res.json();
    pageToken = data.nextPageToken;
    for (const item of data.items || []) {
      const sn = item.snippet;
      const vidId = sn?.resourceId?.videoId;
      if (!vidId) continue;
      videos.push({
        id: vidId,
        title: sn?.title || "Untitled",
        channelTitle: sn?.videoOwnerChannelTitle || sn?.channelTitle || "",
        thumbnail: sn?.thumbnails?.medium?.url || sn?.thumbnails?.default?.url || "",
      });
    }
  } while (pageToken);

  const listRes = await fetch(
    `https://www.googleapis.com/youtube/v3/playlists?part=snippet&id=${playlistId}&key=${YT_API_KEY}`
  );
  if (listRes.ok) {
    const d = await listRes.json();
    playlistTitle = d?.items?.[0]?.snippet?.title || "Playlist";
  } else {
    playlistTitle = "Playlist";
  }

  return { title: playlistTitle, videos };
}

// --- Storage ---
const STORAGE_KEY = "lightning-shuffler-state-v1";
function loadState(): { playlists: Playlist[] } {
  try {
    const raw = localStorage.getItem(STORAGE_KEY);
    if (!raw) return { playlists: [] };
    return JSON.parse(raw);
  } catch {
    return { playlists: [] };
  }
}
function saveState(state: { playlists: Playlist[] }) {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
}

// --- Components ---
function AddPlaylistForm({ onAdd }: { onAdd: (p: Playlist) => void }) {
  const [url, setUrl] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    const pid = extractPlaylistId(url.trim());
    if (!pid) {
      setError("Enter a valid YouTube playlist URL with a 'list' parameter.");
      return;
    }
    setLoading(true);
    try {
      const { title, videos } = await fetchPlaylistItems(pid);
      const playlist: Playlist = {
        id: pid,
        title,
        url: url.trim(),
        videos,
        lastRefreshed: new Date().toISOString(),
      };
      onAdd(playlist);
      setUrl("");
    } catch (err: any) {
      setError(err?.message || "Failed to fetch playlist");
    } finally {
      setLoading(false);
    }
  };

  return (
    <form className="add-form" onSubmit={handleSubmit}>
      <input
        value={url}
        onChange={(e) => setUrl(e.currentTarget.value)}
        placeholder="Paste YouTube playlist URL"
      />
      <button type="submit" disabled={loading}>{loading ? "Adding..." : "Add"}</button>
      {error && <div className="error">{error}</div>}
    </form>
  );
}

function Sidebar({
  playlists,
  currentVideoId,
  onSelectVideo,
  search,
  setSearch,
}: {
  playlists: Playlist[];
  currentVideoId: string | null;
  onSelectVideo: (v: Video) => void;
  search: string;
  setSearch: (s: string) => void;
}) {
  const flattened = useMemo(
    () => playlists.flatMap((p) => p.videos.map((v) => ({ ...v, _pid: p.id, _ptitle: p.title } as any))),
    [playlists]
  ) as (Video & { _pid: string; _ptitle: string })[];

  const filtered = useMemo(() => {
    const q = search.trim().toLowerCase();
    if (!q) return flattened;
    return flattened.filter((v) =>
      v.title.toLowerCase().includes(q) || v.channelTitle.toLowerCase().includes(q)
    );
  }, [flattened, search]);

  return (
    <aside className="sidebar">
      <div className="search-bar">
        <input value={search} onChange={(e) => setSearch(e.currentTarget.value)} placeholder="Search videos or authors" />
      </div>
      <div className="video-list">
        {filtered.map((v) => (
          <div
            key={v.id + v._pid}
            className={"video-item" + (currentVideoId === v.id ? " active" : "")}
            onClick={() => onSelectVideo(v)}
            title={`${v.title} • ${v.channelTitle} — ${v._ptitle}`}
          >
            <img src={v.thumbnail} alt="thumb" />
            <div className="meta">
              <div className="title">{v.title}</div>
              <div className="sub">{v.channelTitle}</div>
            </div>
          </div>
        ))}
        {filtered.length === 0 && <div className="empty">No results</div>}
      </div>
    </aside>
  );
}

function Tooltip({ label }: { label: string }) {
  return (
    <span className="tooltip">{label}</span>
  );
}

function IconButton({
  onClick,
  onContextMenu,
  disabled,
  active,
  label,
  children
}: {
  onClick?: () => void;
  onContextMenu?: (e: React.MouseEvent) => void;
  disabled?: boolean;
  active?: boolean;
  label: string;
  children: React.ReactNode;
}) {
  return (
    <button
      className={`icon-btn ${active ? "active" : ""}`}
      onClick={onClick}
      onContextMenu={onContextMenu}
      disabled={disabled}
      aria-label={label}
      title={label}
    >
      {children}
      <Tooltip label={label} />
    </button>
  );
}

function Controls({ playing, setPlaying, onPrev, onNext, onReshuffle, setLoopCount, playerRef, loopCount }: { playing: boolean; setPlaying: (v: boolean) => void; onPrev: () => void; onNext: () => void; onReshuffle: () => void; setLoopCount: (n: number) => void; playerRef: React.RefObject<HTMLIFrameElement>; loopCount: number; }) {
  const togglePlay = () => {
    if (!playerRef.current) return;
    const action = playing ? "pauseVideo" : "playVideo";
    playerRef.current.contentWindow?.postMessage(JSON.stringify({ event: "command", func: action, args: [] }), "*");
    setPlaying(!playing);
  };

  const handleLoopLeftClick = () => {
    // Left click: toggle infinite loop (loopCount = -1 for infinite, 0 for off)
    setLoopCount(loopCount === -1 ? 0 : -1);
  };

  const handleLoopRightClick = (e: React.MouseEvent) => {
    e.preventDefault();
    // Right click: increment counter (1, 2, 3, etc.)
    if (loopCount <= 0) {
      setLoopCount(1);
    } else if (loopCount < 99) { // Cap at 99 to prevent UI issues
      setLoopCount(loopCount + 1);
    }
  };

  const getLoopLabel = () => {
    if (loopCount === -1) return "Loop: Infinite";
    if (loopCount > 0) return `Loop: ${loopCount} times`;
    return "Loop: Off";
  };

  return (
    <div className="controls">
      <IconButton label="Previous" onClick={onPrev}><IconPlayerSkipBack size={26} /></IconButton>
      <IconButton label={playing ? "Pause" : "Play"} onClick={togglePlay}>{playing ? <IconPlayerPause size={28} /> : <IconPlayerPlay size={28} />}</IconButton>
      <IconButton label="Next" onClick={onNext}><IconPlayerSkipForward size={26} /></IconButton>
      <IconButton label="Shuffle" onClick={onReshuffle}><IconArrowsShuffle size={24} /></IconButton>
      <IconButton
        label={getLoopLabel()}
        onClick={handleLoopLeftClick}
        onContextMenu={handleLoopRightClick}
        active={loopCount !== 0}
      >
        <div className="loop-icon-container">
          <IconRepeat size={24} />
          {loopCount !== 0 && (
            <span className="loop-count">
              {loopCount === -1 ? "∞" : loopCount}
            </span>
          )}
        </div>
      </IconButton>
    </div>
  );
}

function App() {
  const [state, setState] = useState<{ playlists: Playlist[] }>(() => loadState());
  const [search, setSearch] = useState("");
  const [queue, setQueue] = useState<Video[]>([]);
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const [playing, setPlaying] = useState(true);
  const [loopCount, setLoopCount] = useState(0);
  const playerRef = useRef<HTMLIFrameElement | null>(null);

  useEffect(() => { saveState(state); }, [state]);

  const onPrev = React.useCallback(() => {
    setCurrentIndex((i) => Math.max(0, i - 1));
  }, []);

  const onNext = React.useCallback(() => {
    setCurrentIndex((idx) => {
      const isLast = idx >= queue.length - 1;
      if (isLast) {
        // Handle different loop modes:
        // loopCount = -1: infinite loop
        // loopCount > 0: loop N times (decrement counter)
        // loopCount = 0: no loop
        if (loopCount === -1) {
          return 0; // Infinite loop, restart from beginning
        } else if (loopCount > 0) {
          setLoopCount(loopCount - 1); // Decrement loop counter
          return 0; // Restart from beginning
        } else {
          return idx; // No loop, stay at last video
        }
      }
      return idx + 1;
    });
    setPlaying(true);
  }, [queue.length, loopCount]);

  const reshuffleQueue = React.useCallback(() => {
    if (!queue.length) return;
    const current = queue[currentIndex];
    const rest = queue.filter((_, i) => i !== currentIndex);
    for (let i = rest.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [rest[i], rest[j]] = [rest[j], rest[i]];
    }
    const newQ = [current, ...rest];
    setQueue(newQ);
    setCurrentIndex(0);
  }, [queue, currentIndex]);

  // Keyboard shortcuts: Space(play/pause), ArrowLeft(prev), ArrowRight(next), R(reshuffle), L(loop)
  useEffect(() => {
    const handler = (e: KeyboardEvent) => {
      if (e.target && (e.target as HTMLElement).tagName === "INPUT") return;
      if (e.code === "Space") {
        e.preventDefault();
        if (playerRef.current) {
          const action = playing ? "pauseVideo" : "playVideo";
          playerRef.current.contentWindow?.postMessage(JSON.stringify({ event: "command", func: action, args: [] }), "*");
          setPlaying(!playing);
        }
      } else if (e.code === "ArrowLeft") {
        e.preventDefault();
        onPrev();
      } else if (e.code === "ArrowRight") {
        e.preventDefault();
        onNext();
      } else if (e.key.toLowerCase() === "r") {
        e.preventDefault();
        reshuffleQueue();
      } else if (e.key.toLowerCase() === "l") {
        e.preventDefault();
        // L key toggles infinite loop (same as left click)
        setLoopCount((v) => (v === -1 ? 0 : -1));
      }
    };
    window.addEventListener("keydown", handler);
    return () => window.removeEventListener("keydown", handler);
  }, [playing, onPrev, onNext, reshuffleQueue]);

  const currentVideo: Video | null = queue[currentIndex] || null;

  const onAddPlaylist = (p: Playlist) => {
    setState((s) => ({ playlists: [...s.playlists.filter((x) => x.id !== p.id), p] }));
    setQueue((q) => (q.length ? q : p.videos));
    setCurrentIndex(0);
  };

  const onSelectVideo = (v: Video) => {
    const idx = queue.findIndex((x) => x.id === v.id);
    if (idx >= 0) setCurrentIndex(idx);
    else setQueue([v, ...queue]);
  };

  // MediaSession API integration for hardware media keys
  useEffect(() => {
    if (!('mediaSession' in navigator)) return;

    // Set up action handlers
    navigator.mediaSession.setActionHandler('play', () => {
      if (playerRef.current) {
        playerRef.current.contentWindow?.postMessage(JSON.stringify({ event: "command", func: "playVideo", args: [] }), "*");
        setPlaying(true);
      }
    });

    navigator.mediaSession.setActionHandler('pause', () => {
      if (playerRef.current) {
        playerRef.current.contentWindow?.postMessage(JSON.stringify({ event: "command", func: "pauseVideo", args: [] }), "*");
        setPlaying(false);
      }
    });

    navigator.mediaSession.setActionHandler('previoustrack', () => {
      onPrev();
    });

    navigator.mediaSession.setActionHandler('nexttrack', () => {
      onNext();
    });

    // Cleanup on unmount
    return () => {
      if ('mediaSession' in navigator) {
        navigator.mediaSession.setActionHandler('play', null);
        navigator.mediaSession.setActionHandler('pause', null);
        navigator.mediaSession.setActionHandler('previoustrack', null);
        navigator.mediaSession.setActionHandler('nexttrack', null);
      }
    };
  }, [onPrev, onNext]);

  // Update MediaSession metadata when current video changes
  useEffect(() => {
    if (!('mediaSession' in navigator) || !currentVideo) return;

    navigator.mediaSession.metadata = new MediaMetadata({
      title: currentVideo.title,
      artist: currentVideo.channelTitle,
      album: 'Lightning Shuffler',
      artwork: [
        {
          src: currentVideo.thumbnail,
          sizes: '320x180',
          type: 'image/jpeg',
        },
      ],
    });

    // Update tray tooltip with current video info
    const tooltip = `Lightning Shuffler - ${currentVideo.title}`;
    invoke('update_tray_tooltip', { tooltip }).catch(console.error);
  }, [currentVideo]);

  // Update MediaSession playback state
  useEffect(() => {
    if (!('mediaSession' in navigator)) return;

    navigator.mediaSession.playbackState = playing ? 'playing' : 'paused';
  }, [playing]);

  // Handle window minimize/close to tray
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+M to minimize to tray
      if (e.ctrlKey && e.key.toLowerCase() === 'm') {
        e.preventDefault();
        invoke('hide_to_tray');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <div className="app">
      <Sidebar
        playlists={state.playlists}
        currentVideoId={currentVideo?.id || null}
        onSelectVideo={onSelectVideo}
        search={search}
        setSearch={setSearch}
      />
      <div className="main">
        <header className="topbar">
          <h1>Lightning Shuffler</h1>
          <AddPlaylistForm onAdd={onAddPlaylist} />
        </header>
        <div className="player">
          {currentVideo ? (
            <iframe
              ref={playerRef}
              className="yt-frame"
              src={`https://www.youtube.com/embed/${currentVideo.id}?enablejsapi=1&rel=0`}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            />
          ) : (
            <div className="player placeholder">Add a playlist to start</div>
          )}
        </div>
        <Controls
          onPrev={onPrev}
          onNext={onNext}
          onReshuffle={reshuffleQueue}
          playing={playing}
          setPlaying={setPlaying}
          setLoopCount={setLoopCount}
          playerRef={playerRef}
          loopCount={loopCount}
        />
      </div>
    </div>
  );
}

export default App;
