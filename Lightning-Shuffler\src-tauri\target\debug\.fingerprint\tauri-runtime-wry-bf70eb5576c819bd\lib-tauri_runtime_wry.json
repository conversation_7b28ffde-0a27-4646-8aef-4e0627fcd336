{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 5488884035266712508, "deps": [[376837177317575824, "softbuffer", false, 16278298829636407195], [654232091421095663, "tauri_utils", false, 1219994895049865922], [2013030631243296465, "webview2_com", false, 16292405638108799478], [3150220818285335163, "url", false, 2932393668862457855], [3722963349756955755, "once_cell", false, 694654376419026606], [4143744114649553716, "raw_window_handle", false, 4572721544728011781], [5986029879202738730, "log", false, 769167750787707049], [8826339825490770380, "tao", false, 13383114315730377860], [9010263965687315507, "http", false, 5530258970695073684], [9141053277961803901, "wry", false, 1529572201178732405], [12304025191202589669, "build_script_build", false, 8571479264771667986], [12943761728066819757, "tauri_runtime", false, 4822173334274463840], [14585479307175734061, "windows", false, 11065999775011887023]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-bf70eb5576c819bd\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}