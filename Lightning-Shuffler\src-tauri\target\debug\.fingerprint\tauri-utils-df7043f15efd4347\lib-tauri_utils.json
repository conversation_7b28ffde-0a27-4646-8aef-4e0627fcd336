{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 12142214438820330329, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 10928541493328989523], [3150220818285335163, "url", false, 2932393668862457855], [3191507132440681679, "serde_untagged", false, 483567500444238779], [4071963112282141418, "serde_with", false, 9712996938505171913], [4899080583175475170, "semver", false, 15127031173250709614], [5986029879202738730, "log", false, 769167750787707049], [6606131838865521726, "ctor", false, 17171881424293827261], [7170110829644101142, "json_patch", false, 10251711036669688981], [8319709847752024821, "uuid", false, 7602980692828604279], [9010263965687315507, "http", false, 5530258970695073684], [9451456094439810778, "regex", false, 17299367412684476405], [9556762810601084293, "brotli", false, 17896885105989400910], [9689903380558560274, "serde", false, 5874487516112888390], [10806645703491011684, "thiserror", false, 14013279725810500109], [11989259058781683633, "dunce", false, 6706533162972636214], [13625485746686963219, "anyhow", false, 1399948433417357107], [15609422047640926750, "toml", false, 12116947827558369309], [15622660310229662834, "walkdir", false, 6763526243238314991], [15932120279885307830, "memchr", false, 13323802158942823166], [16362055519698394275, "serde_json", false, 9609641283525214307], [17146114186171651583, "infer", false, 1885993274616000846], [17155886227862585100, "glob", false, 3292550539767500518], [17186037756130803222, "phf", false, 11544104511974823957]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-df7043f15efd4347\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}